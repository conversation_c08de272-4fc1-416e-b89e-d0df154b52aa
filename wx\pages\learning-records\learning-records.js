/**
 * 学习记录页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    records: [],
    summary: {
      totalDays: 0,
      totalWords: 0,
      totalMastered: 0,
      averageAccuracy: 0
    },
    currentMonth: '',
    monthRecords: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    const currentChild = app.getCurrentChild();
    if (!currentChild) {
      wx.showToast({
        title: '请先设置幼儿信息',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const now = new Date();
    const currentMonth = `${now.getFullYear()}年${now.getMonth() + 1}月`;

    this.setData({
      currentChild,
      currentMonth
    });

    this.loadData();
  },

  /**
   * 加载数据
   */
  loadData: function() {
    this.loadLearningRecords();
    this.calculateSummary();
  },

  /**
   * 加载学习记录
   */
  loadLearningRecords: function() {
    const childId = this.data.currentChild.child_id;
    const allRecords = app.dataManager.getLearningRecords(childId);
    
    // 按日期倒序排列
    const sortedRecords = allRecords.sort((a, b) => 
      new Date(b.learn_date) - new Date(a.learn_date)
    );

    // 格式化记录数据
    const formattedRecords = sortedRecords.map(record => ({
      ...record,
      accuracy: record.word_count > 0 ? 
        Math.round((record.mastered_count / record.word_count) * 100) : 0,
      dateDisplay: this.formatDate(record.learn_date),
      weekday: this.getWeekday(record.learn_date)
    }));

    // 筛选当月记录
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    const monthRecords = formattedRecords.filter(record => {
      const recordDate = new Date(record.learn_date);
      return recordDate.getFullYear() === currentYear && 
             recordDate.getMonth() + 1 === currentMonth;
    });

    this.setData({
      records: formattedRecords.slice(0, 20), // 只显示最近20条
      monthRecords
    });
  },

  /**
   * 格式化日期
   */
  formatDate: function(dateStr) {
    const date = new Date(dateStr);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return `${month}月${day}日`;
  },

  /**
   * 获取星期
   */
  getWeekday: function(dateStr) {
    const date = new Date(dateStr);
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    return weekdays[date.getDay()];
  },

  /**
   * 计算统计摘要 - 修复学习天数计算逻辑
   */
  calculateSummary: function() {
    const records = this.data.records;
    const childId = this.data.currentChild.child_id;
    const today = app.dataManager.getCurrentDate();

    if (records.length === 0) {
      // 即使没有学习记录，也要检查今天是否有复习活动
      const todayTasks = app.ebbinghausManager.getTodayReviewTasks(childId);
      const todayHasActivity = todayTasks.some(task => task.completed === true);

      this.setData({
        summary: {
          totalDays: todayHasActivity ? 1 : 0,
          totalWords: 0,
          totalMastered: 0,
          averageAccuracy: 0
        }
      });
      return;
    }

    // 正确计算学习天数：基于唯一日期而不是记录数量
    const uniqueDates = new Set(records.map(record => record.learn_date));

    // 检查今天是否有复习活动但还没有学习记录
    const todayTasks = app.ebbinghausManager.getTodayReviewTasks(childId);
    const todayHasActivity = todayTasks.some(task => task.completed === true);

    if (todayHasActivity && !uniqueDates.has(today)) {
      uniqueDates.add(today);
    }

    const totalDays = uniqueDates.size;
    const totalWords = records.reduce((sum, record) => sum + record.word_count, 0);
    const totalMastered = records.reduce((sum, record) => sum + record.mastered_count, 0);
    const averageAccuracy = totalWords > 0 ?
      Math.round((totalMastered / totalWords) * 100) : 0;

    this.setData({
      summary: {
        totalDays,
        totalWords,
        totalMastered,
        averageAccuracy
      }
    });
  },

  /**
   * 查看记录详情
   */
  viewRecordDetail: function(e) {
    const recordId = e.currentTarget.dataset.recordId;
    const record = this.data.records.find(r => r.record_id === recordId);
    
    if (!record) return;

    let content = `学习日期：${record.learn_date}\n`;
    content += `学习字词：${record.word_count}个\n`;
    content += `掌握字词：${record.mastered_count}个\n`;
    content += `正确率：${record.accuracy}%\n`;
    
    if (record.weak_words && record.weak_words.length > 0) {
      content += `\n薄弱字词：\n${record.weak_words.join('、')}`;
    }

    wx.showModal({
      title: '学习记录详情',
      content: content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 删除记录
   */
  deleteRecord: function(e) {
    const recordId = e.currentTarget.dataset.recordId;
    const record = this.data.records.find(r => r.record_id === recordId);
    
    wx.showModal({
      title: '确认删除',
      content: `确定要删除 ${record.learn_date} 的学习记录吗？`,
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: (res) => {
        if (res.confirm) {
          this.performDeleteRecord(recordId);
        }
      }
    });
  },

  /**
   * 执行删除记录
   */
  performDeleteRecord: function(recordId) {
    wx.showLoading({
      title: '删除中...'
    });

    const allRecords = app.dataManager.getLearningRecords();
    const filteredRecords = allRecords.filter(record => record.record_id !== recordId);
    
    try {
      wx.setStorageSync('learning_records', filteredRecords);
      
      wx.hideLoading();
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      });

      this.loadData();
    } catch (error) {
      wx.hideLoading();
      console.error('删除记录失败:', error);
      wx.showToast({
        title: '删除失败',
        icon: 'none'
      });
    }
  },

  /**
   * 导出记录
   */
  exportRecords: function() {
    if (this.data.records.length === 0) {
      wx.showToast({
        title: '暂无记录可导出',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '导出中...'
    });

    try {
      const childName = this.data.currentChild.child_name;
      let exportText = `${childName} 的学习记录\n`;
      exportText += `导出时间：${app.dataManager.getCurrentDateTime()}\n\n`;
      
      exportText += `学习统计：\n`;
      exportText += `总学习天数：${this.data.summary.totalDays}天\n`;
      exportText += `总学习字词：${this.data.summary.totalWords}个\n`;
      exportText += `总掌握字词：${this.data.summary.totalMastered}个\n`;
      exportText += `平均正确率：${this.data.summary.averageAccuracy}%\n\n`;
      
      exportText += `详细记录：\n`;
      this.data.records.forEach(record => {
        exportText += `${record.learn_date} (${record.weekday})\n`;
        exportText += `  学习：${record.word_count}个，掌握：${record.mastered_count}个，正确率：${record.accuracy}%\n`;
        if (record.weak_words && record.weak_words.length > 0) {
          exportText += `  薄弱字词：${record.weak_words.join('、')}\n`;
        }
        exportText += '\n';
      });

      // 复制到剪贴板
      wx.setClipboardData({
        data: exportText,
        success: () => {
          wx.hideLoading();
          wx.showToast({
            title: '已复制到剪贴板',
            icon: 'success'
          });
        },
        fail: () => {
          wx.hideLoading();
          wx.showToast({
            title: '导出失败',
            icon: 'none'
          });
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('导出失败:', error);
      wx.showToast({
        title: '导出失败',
        icon: 'none'
      });
    }
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
