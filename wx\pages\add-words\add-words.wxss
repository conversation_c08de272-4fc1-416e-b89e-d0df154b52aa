/* 添加字词页面样式 */

.add-words-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 幼儿信息栏 */
.child-info-bar {
  display: flex;
  align-items: center;
  gap: 24rpx;
  background: white;
  padding: 32rpx;
  border-radius: 24rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.child-avatar {
  font-size: 48rpx;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--soft-green);
  border-radius: 50%;
}

.child-details {
  flex: 1;
}

.child-name {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 8rpx;
}

.child-class {
  font-size: 24rpx;
  color: var(--text-light);
}



/* 输入区域 */
.input-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}



/* 批量输入 */
.batch-input-container {
  margin-bottom: 24rpx;
}

.batch-input {
  width: 100%;
  min-height: 200rpx;
  padding: 24rpx;
  border: 2px solid var(--border-light);
  border-radius: 24rpx;
  font-size: 28rpx;
  background: var(--warm-beige);
  box-sizing: border-box;
  line-height: 1.6;
}

.batch-input:focus {
  border-color: var(--primary-green);
}

/* 分隔符提示 */
.separator-tips {
  margin-bottom: 32rpx;
  padding: 20rpx;
  background: var(--soft-green);
  border-radius: 16rpx;
}

.tips-title {
  font-size: 24rpx;
  color: var(--text-dark);
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;
}

.separator-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.separator-item {
  background: white;
  color: var(--text-dark);
  padding: 6rpx 16rpx;
  border-radius: 12rpx;
  font-size: 22rpx;
  border: 1px solid var(--border-light);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 16rpx;
}

.action-buttons button {
  flex: 1;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 预览区域 */
.preview-section {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.word-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.preview-word-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  background: var(--soft-green);
  padding: 12rpx 20rpx;
  border-radius: 20rpx;
  border: 1px solid var(--light-green);
}

.word-text {
  font-size: 28rpx;
  color: var(--text-dark);
  font-weight: 500;
  flex: 1;
}

.remove-word-btn {
  width: 40rpx !important;
  height: 40rpx !important;
  min-width: 40rpx !important;
  min-height: 40rpx !important;
  max-width: 40rpx !important;
  max-height: 40rpx !important;
  background: var(--text-light);
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  padding: 0 !important;
  margin: 0;
  line-height: 1;
  overflow: hidden;
}

.remove-word-btn::after {
  border: none;
}

/* 使用说明 */
.usage-guide {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
}

.guide-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.guide-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.guide-item {
  font-size: 26rpx;
  color: var(--text-light);
  line-height: 1.5;
}

/* 底部操作 */
.bottom-actions {
  padding: 32rpx 0;
}

.view-library-btn {
  width: 100%;
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .action-buttons {
    flex-direction: column;
  }
  
  .action-buttons button {
    flex: none;
  }
}
