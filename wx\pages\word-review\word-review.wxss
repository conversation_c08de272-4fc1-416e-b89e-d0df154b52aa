/* 字词复习页面样式 */

.review-container {
  height: 100vh;
  background: var(--warm-beige);
  display: flex;
  flex-direction: column;
  padding: 32rpx;
}

/* 进度区域 */
.progress-section {
  margin-bottom: 40rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: var(--text-light);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-green);
}

/* 字词显示区域 */
.word-display-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.word-display {
  margin-bottom: 60rpx;
}

.word-description {
  margin-top: 32rpx;
}

.weak-indicator {
  font-size: 24rpx;
  color: var(--orange);
  margin-top: 8rpx;
  padding: 8rpx 16rpx;
  background: #FFF3CD;
  border-radius: 12rpx;
  display: inline-block;
}

.word-type {
  font-size: 32rpx;
  color: var(--text-light);
  margin-bottom: 8rpx;
}

.review-stage {
  font-size: 24rpx;
  color: var(--soft-orange);
  background: rgba(244, 162, 97, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

/* 互动指导 */
.interaction-guide {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: var(--shadow-soft);
  border: 1px solid var(--border-light);
  max-width: 500rpx;
  margin-bottom: 40rpx;
}

.guide-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
}

.guide-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 24rpx;
}

.guide-content {
  text-align: left;
}

.guide-item {
  font-size: 28rpx;
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 12rpx;
}

/* 操作区域 */
.action-area {
  margin-top: auto;
}

.main-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  padding: 32rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  border-radius: 24rpx;
}

/* 导航区域 */
.nav-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-top: 1px solid var(--border-light);
}

.nav-btn {
  background: transparent;
  color: var(--primary-green);
  border: none;
  font-size: 28rpx;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.nav-btn.disabled {
  color: var(--text-light);
  opacity: 0.5;
}

.nav-btn:not(.disabled):active {
  background: var(--soft-green);
  transform: scale(0.95);
}

/* 进度指示点 */
.progress-dots {
  display: flex;
  gap: 12rpx;
  max-width: 300rpx;
  overflow-x: auto;
  padding: 8rpx;
}

.progress-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.progress-dot.completed {
  background: var(--primary-green);
}

.progress-dot.current {
  background: var(--soft-orange);
  transform: scale(1.2);
}

.progress-dot.pending {
  background: var(--border-light);
}

/* 完成弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 1000;
}

.modal-overlay.show {
  opacity: 1;
  visibility: visible;
}

.complete-modal {
  background: white;
  border-radius: 32rpx;
  margin: 32rpx;
  max-width: 600rpx;
  width: 100%;
  transform: translateY(60rpx);
  transition: transform 0.3s ease;
}

.modal-overlay.show .complete-modal {
  transform: translateY(0);
}

.modal-content {
  padding: 60rpx 40rpx;
  text-align: center;
}

.celebration-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.modal-title {
  font-size: 40rpx;
  font-weight: bold;
  color: var(--text-dark);
  margin-bottom: 16rpx;
}

.modal-subtitle {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 48rpx;
}

.results-summary {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32rpx;
  margin-bottom: 48rpx;
}

.result-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.result-number {
  font-size: 48rpx;
  font-weight: bold;
}

.result-number.mastered {
  color: var(--primary-green);
}

.result-number.need-review {
  color: var(--soft-orange);
}

.result-label {
  font-size: 24rpx;
  color: var(--text-light);
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  gap: 16rpx;
  width: 100%;
}

.modal-btn {
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .main-actions {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
  
  .modal-actions {
    grid-template-columns: 1fr;
  }
  
  .results-summary {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
}
