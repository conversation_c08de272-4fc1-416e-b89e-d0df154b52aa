/**
 * 首页 - 今日复习
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    todayWords: [],
    todayProgress: {
      total: 0,
      completed: 0,
      percentage: 0
    },
    weeklyStats: {
      learnedWords: 0,
      pendingReview: 0,
      studyDays: 0
    },
    greeting: '',
    greetingIcon: '',
    todayDate: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.refreshData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    // 检查是否需要显示引导页
    const hasLaunched = wx.getStorageSync('hasLaunched');
    const onboardingCompleted = wx.getStorageSync('onboardingCompleted');
    
    if (!hasLaunched || !onboardingCompleted) {
      wx.redirectTo({
        url: '/pages/onboarding/onboarding'
      });
      return;
    }

    this.setGreeting();
    this.refreshData();
  },

  /**
   * 设置问候语
   */
  setGreeting: function() {
    const hour = new Date().getHours();
    const now = new Date();
    let greeting, icon;

    if (hour < 6) {
      greeting = '夜深了';
      icon = '🌙';
    } else if (hour < 12) {
      greeting = '早上好！';
      icon = '🌞';
    } else if (hour < 18) {
      greeting = '下午好！';
      icon = '☀️';
    } else {
      greeting = '晚上好！';
      icon = '🌆';
    }

    // 设置今日日期
    const todayDate = `${now.getMonth() + 1}月${now.getDate()}日`;

    this.setData({
      greeting,
      greetingIcon: icon,
      todayDate
    });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    const currentChild = app.getCurrentChild();
    
    if (!currentChild) {
      this.showNoChildTip();
      return;
    }

    this.setData({
      currentChild
    });

    this.loadTodayWords();
    this.loadWeeklyStats();
  },

  /**
   * 显示无幼儿提示
   */
  showNoChildTip: function() {
    wx.showModal({
      title: '设置幼儿信息',
      content: '请先添加幼儿信息，以便为您提供个性化的学习服务',
      confirmText: '去添加',
      cancelText: '稍后',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/child-management/child-management?action=add'
          });
        }
      }
    });
  },

  /**
   * 加载今日字词
   */
  loadTodayWords: function() {
    const childId = this.data.currentChild.child_id;
    const today = app.dataManager.getCurrentDate();

    // 获取今日复习任务
    const todayTasks = app.ebbinghausManager.getTodayReviewTasks(childId);

    // 获取今日新添加的字词
    const allWords = app.dataManager.getWordLibrary(childId);
    const todayNewWords = allWords.filter(word =>
      word.create_time && word.create_time.startsWith(today)
    );

    // 获取已存在于复习任务中的字词ID，避免重复
    const existingWordIds = new Set(todayTasks.map(task => task.word_info.word_id));

    // 过滤掉已在复习任务中的新字词
    const filteredNewWords = todayNewWords.filter(word =>
      !existingWordIds.has(word.word_id)
    );

    // 合并今日字词（确保无重复）
    const todayWords = [
      ...todayTasks,
      ...filteredNewWords.map(word => ({
        word_info: word,
        isNewWord: true
      }))
    ];

    // 计算进度 - 修复计算逻辑
    // 对于复习任务，检查是否已完成；对于新字词，检查是否今日已复习
    const completed = todayWords.filter(item => {
      if (item.isNewWord) {
        // 新字词：检查是否今日已复习过
        return item.word_info.last_review_date === today;
      } else {
        // 复习任务：检查任务是否已完成
        return item.completed === true;
      }
    }).length;

    const progress = {
      total: todayWords.length,
      completed: completed,
      percentage: todayWords.length > 0 ? Math.round((completed / todayWords.length) * 100) : 0
    };

    this.setData({
      todayWords: todayWords.slice(0, 4), // 只显示前4个
      todayProgress: progress
    });
  },

  /**
   * 加载本周统计
   */
  loadWeeklyStats: function() {
    const childId = this.data.currentChild.child_id;
    const stats = app.ebbinghausManager.getReviewStatistics(childId);
    
    this.setData({
      weeklyStats: {
        learnedWords: stats.mastered_words,
        pendingReview: stats.today_total - stats.today_completed,
        studyDays: this.calculateStudyDays(childId)
      }
    });
  },

  /**
   * 计算学习天数 - 修复计算逻辑
   */
  calculateStudyDays: function(childId) {
    const records = app.dataManager.getLearningRecords(childId);
    const today = app.dataManager.getCurrentDate();

    // 获取所有学习记录的日期
    const recordDates = new Set(records.map(record => record.learn_date));

    // 检查今天是否有复习活动（即使还没有保存学习记录）
    const todayTasks = app.ebbinghausManager.getTodayReviewTasks(childId);
    const todayHasActivity = todayTasks.some(task => task.completed === true);

    // 如果今天有复习活动但还没有学习记录，也算作学习天数
    if (todayHasActivity && !recordDates.has(today)) {
      recordDates.add(today);
    }

    return recordDates.size;
  },

  /**
   * 添加字词
   */
  onAddWords: function() {
    wx.navigateTo({
      url: '/pages/add-words/add-words'
    });
  },

  /**
   * 开始复习
   */
  onStartReview: function() {
    if (this.data.todayProgress.total === 0) {
      wx.showToast({
        title: '暂无复习任务',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: '/pages/word-review/word-review?type=today'
    });
  },

  /**
   * 查看全部字词
   */
  onViewAllWords: function() {
    wx.navigateTo({
      url: '/pages/word-list/word-list'
    });
  },

  /**
   * 切换幼儿
   */
  onSwitchChild: function() {
    wx.navigateTo({
      url: '/pages/child-management/child-management'
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.refreshData();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  }
});
