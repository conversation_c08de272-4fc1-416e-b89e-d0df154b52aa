/* 复习日历页面样式 */

.calendar-container {
  background: var(--warm-beige);
  min-height: 100vh;
  padding: 32rpx;
}

/* 月份导航 */
.month-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
  padding: 0 16rpx;
}

.month-nav {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.month-nav:active {
  transform: scale(0.95);
  background: var(--soft-green);
}

.nav-icon {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--primary-green);
}

.month-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-dark);
}

/* 月度统计 */
.month-stats {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-green);
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 星期标题 */
.weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  margin-bottom: 16rpx;
}

.weekday {
  text-align: center;
  font-size: 24rpx;
  color: var(--text-light);
  padding: 16rpx 0;
  font-weight: 600;
}

/* 日历网格 */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 32rpx;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 16rpx;
  transition: all 0.3s ease;
  min-height: 100rpx;
}

.calendar-day.current-month {
  background: var(--warm-beige);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: var(--primary-green);
  color: white;
}

.calendar-day.has-tasks {
  background: var(--soft-green);
  border: 2rpx solid var(--light-green);
}

.calendar-day.today.has-tasks {
  background: var(--primary-green);
  border: 2rpx solid var(--primary-green);
}

.calendar-day:active {
  transform: scale(0.95);
}

.day-number {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

/* 任务指示器 */
.task-indicator {
  display: flex;
  align-items: center;
  gap: 4rpx;
  margin-bottom: 4rpx;
}

.task-dot {
  width: 12rpx;
  height: 12rpx;
  border-radius: 50%;
}

.task-dot.pending {
  background: var(--orange);
}

.task-dot.completed {
  background: var(--primary-green);
}

.task-count {
  font-size: 20rpx;
  color: var(--text-dark);
}

/* 完成率指示条 */
.completion-bar {
  width: 80%;
  height: 4rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2rpx;
  overflow: hidden;
}

.completion-fill {
  height: 100%;
  background: var(--primary-green);
  transition: width 0.3s ease;
}

/* 图例说明 */
.legend {
  display: flex;
  justify-content: space-around;
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.legend-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
}

.legend-dot.pending {
  background: var(--orange);
}

.legend-dot.completed {
  background: var(--primary-green);
}

.legend-dot.today {
  background: var(--primary-green);
  border: 2rpx solid white;
  box-shadow: 0 0 0 2rpx var(--primary-green);
}

.legend-text {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 任务详情弹窗 */
.task-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.task-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: white;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 80vh;
  overflow: hidden;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.task-modal.show .modal-content {
  transform: translateY(0);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid var(--border-light);
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.close-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: var(--text-light);
  border-radius: 50%;
  background: var(--warm-beige);
}

.modal-body {
  padding: 32rpx;
  max-height: 50vh;
  overflow-y: auto;
}

.task-summary {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 24rpx;
  text-align: center;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.task-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  background: var(--warm-beige);
  border-radius: 16rpx;
}

.task-word {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-dark);
}

.task-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8rpx;
}

.task-stage {
  font-size: 24rpx;
  color: var(--text-light);
}

.task-status {
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-weight: 600;
}

.task-status.completed {
  background: var(--soft-green);
  color: var(--primary-green);
}

.task-status.pending {
  background: #FFF3CD;
  color: var(--orange);
}

.modal-footer {
  display: flex;
  gap: 16rpx;
  padding: 32rpx;
  border-top: 1rpx solid var(--border-light);
}

.btn-secondary,
.btn-primary {
  flex: 1;
  height: 88rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}

.btn-secondary {
  background: var(--warm-beige);
  color: var(--text-dark);
}

.btn-primary {
  background: var(--primary-green);
  color: white;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .calendar-container {
    padding: 24rpx;
  }
  
  .month-stats {
    padding: 24rpx;
  }
  
  .calendar-grid {
    padding: 16rpx;
  }
  
  .calendar-day {
    min-height: 80rpx;
  }
  
  .day-number {
    font-size: 24rpx;
  }
}
