/**
 * 复习日历页面
 */

const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    currentChild: null,
    currentYear: 0,
    currentMonth: 0,
    monthName: '',
    calendarDays: [],
    selectedDate: '',
    selectedTasks: [],
    showTaskModal: false,
    monthStats: {
      totalTasks: 0,
      completedTasks: 0,
      completionRate: 0
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initPage();
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    this.loadCalendarData();
  },

  /**
   * 初始化页面
   */
  initPage: function() {
    const currentChild = app.getCurrentChild();
    if (!currentChild) {
      wx.showToast({
        title: '请先设置幼儿信息',
        icon: 'none'
      });
      wx.navigateBack();
      return;
    }

    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    const monthName = `${currentYear}年${currentMonth}月`;

    this.setData({
      currentChild,
      currentYear,
      currentMonth,
      monthName
    });

    this.loadCalendarData();
  },

  /**
   * 加载日历数据
   */
  loadCalendarData: function() {
    const { currentChild, currentYear, currentMonth } = this.data;
    
    // 获取复习日历数据
    const calendarData = app.ebbinghausManager.getReviewCalendar(
      currentChild.child_id, 
      currentYear, 
      currentMonth
    );

    // 生成日历天数数组
    const calendarDays = this.generateCalendarDays(currentYear, currentMonth, calendarData);
    
    // 计算月度统计
    const monthStats = this.calculateMonthStats(calendarData);

    this.setData({
      calendarDays,
      monthStats
    });
  },

  /**
   * 生成日历天数数组
   */
  generateCalendarDays: function(year, month, calendarData) {
    const firstDay = new Date(year, month - 1, 1);
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();
    const startWeekday = firstDay.getDay(); // 0=周日, 1=周一...
    
    const days = [];
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;

    // 添加上月末尾的空白天数（从周一开始）
    const startOffset = startWeekday === 0 ? 6 : startWeekday - 1;
    for (let i = 0; i < startOffset; i++) {
      days.push({
        date: '',
        dateStr: '',
        isCurrentMonth: false,
        isToday: false,
        hasTasks: false,
        total: 0,
        completed: 0
      });
    }

    // 添加当月的天数
    for (let date = 1; date <= daysInMonth; date++) {
      const dateStr = `${year}-${String(month).padStart(2, '0')}-${String(date).padStart(2, '0')}`;
      const dayData = calendarData[dateStr] || { total: 0, completed: 0 };
      
      days.push({
        date,
        dateStr,
        isCurrentMonth: true,
        isToday: dateStr === todayStr,
        hasTasks: dayData.total > 0,
        total: dayData.total,
        completed: dayData.completed,
        completionRate: dayData.total > 0 ? Math.round((dayData.completed / dayData.total) * 100) : 0
      });
    }

    // 补齐剩余位置到42个（6周）
    while (days.length < 42) {
      days.push({
        date: '',
        dateStr: '',
        isCurrentMonth: false,
        isToday: false,
        hasTasks: false,
        total: 0,
        completed: 0
      });
    }

    return days;
  },

  /**
   * 计算月度统计
   */
  calculateMonthStats: function(calendarData) {
    let totalTasks = 0;
    let completedTasks = 0;

    Object.values(calendarData).forEach(dayData => {
      totalTasks += dayData.total;
      completedTasks += dayData.completed;
    });

    return {
      totalTasks,
      completedTasks,
      completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0
    };
  },

  /**
   * 日期点击事件
   */
  onDateTap: function(e) {
    const { dateStr, hasTasks } = e.currentTarget.dataset;
    
    if (!dateStr || !hasTasks) return;

    this.setData({
      selectedDate: dateStr
    });

    this.loadDateTasks(dateStr);
  },

  /**
   * 加载指定日期的任务
   */
  loadDateTasks: function(dateStr) {
    const childId = this.data.currentChild.child_id;
    const allPlans = app.dataManager.getReviewPlans(childId);
    const dateTasks = allPlans.filter(plan => plan.review_date === dateStr);
    
    if (dateTasks.length === 0) {
      wx.showToast({
        title: '该日期无复习任务',
        icon: 'none'
      });
      return;
    }

    // 获取字词信息
    const wordLibrary = app.dataManager.getWordLibrary(childId);
    const tasksWithWords = dateTasks.map(task => {
      const word = wordLibrary.find(w => w.word_id === task.word_id);
      return {
        ...task,
        word_info: word
      };
    }).filter(task => task.word_info);

    this.setData({
      selectedTasks: tasksWithWords,
      showTaskModal: true
    });
  },

  /**
   * 关闭任务弹窗
   */
  closeTaskModal: function() {
    this.setData({
      showTaskModal: false,
      selectedTasks: []
    });
  },

  /**
   * 上一月
   */
  prevMonth: function() {
    let { currentYear, currentMonth } = this.data;
    
    currentMonth--;
    if (currentMonth < 1) {
      currentMonth = 12;
      currentYear--;
    }

    const monthName = `${currentYear}年${currentMonth}月`;

    this.setData({
      currentYear,
      currentMonth,
      monthName
    });

    this.loadCalendarData();
  },

  /**
   * 下一月
   */
  nextMonth: function() {
    let { currentYear, currentMonth } = this.data;
    
    currentMonth++;
    if (currentMonth > 12) {
      currentMonth = 1;
      currentYear++;
    }

    const monthName = `${currentYear}年${currentMonth}月`;

    this.setData({
      currentYear,
      currentMonth,
      monthName
    });

    this.loadCalendarData();
  },

  /**
   * 开始复习指定日期的任务
   */
  startDateReview: function() {
    const { selectedDate, selectedTasks } = this.data;
    
    if (selectedTasks.length === 0) {
      wx.showToast({
        title: '暂无复习任务',
        icon: 'none'
      });
      return;
    }

    // 关闭弹窗
    this.closeTaskModal();

    // 跳转到复习页面，传递日期参数
    wx.navigateTo({
      url: `/pages/word-review/word-review?type=date&date=${selectedDate}`
    });
  }
});
