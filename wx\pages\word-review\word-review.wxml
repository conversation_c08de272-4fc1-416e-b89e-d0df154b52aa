<!--字词复习页面-->
<view class="review-container">
  <!-- 进度指示器 -->
  <view class="progress-section">
    <view class="progress-info">
      <text class="progress-label">复习进度</text>
      <text class="progress-text">{{currentIndex + 1}}/{{totalCount}}</text>
    </view>
    <view class="progress-bar">
      <view class="progress-fill" style="width: {{progress}}%"></view>
    </view>
  </view>

  <!-- 字词显示区域 -->
  <view class="word-display-area">
    <!-- 大字显示 -->
    <view class="word-display" wx:if="{{currentWord}}">
      <view class="big-character">{{currentWord.word_text}}</view>
      <view class="word-description">
        <view class="word-type">
          {{currentWord.isNewWord ? '今日新学字词' :
            currentWord.isWeakWord ? '薄弱字词专项复习' :
            currentWord.isDateReview ? '指定日期复习' : '复习字词'}}
        </view>
        <view class="review-stage" wx:if="{{currentWord.reviewStage}}">
          第{{currentWord.reviewStage}}次复习
        </view>
        <view class="weak-indicator" wx:if="{{currentWord.isWeakWord}}">
          已复习{{currentWord.review_times}}次
        </view>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="interaction-guide">
      <view class="guide-icon">🌸</view>
      <view class="guide-title">和孩子一起认识这个字</view>
      <view class="guide-content">
        <view class="guide-item">• 这是什么字？</view>
        <view class="guide-item">• 这个字可以组成什么词？</view>
        <view class="guide-item">• 在什么场景里会用到这个字？</view>
      </view>
    </view>
  </view>

  <!-- 操作按钮区域 -->
  <view class="action-area">
    <!-- 主要操作按钮 -->
    <view class="main-actions">
      <button class="btn-primary action-btn" bindtap="onMastered">
        ✅ 已掌握
      </button>
      <button class="btn-secondary action-btn" bindtap="onNeedReview">
        🔄 需复习
      </button>
    </view>

    <!-- 导航按钮 -->
    <view class="nav-actions">
      <button 
        class="nav-btn {{currentIndex === 0 ? 'disabled' : ''}}" 
        bindtap="prevWord"
        disabled="{{currentIndex === 0}}"
      >
        ← 上一个
      </button>
      
      <!-- 进度指示点 -->
      <view class="progress-dots">
        <view 
          wx:for="{{words}}" 
          wx:key="index"
          class="progress-dot {{index < currentIndex ? 'completed' : (index === currentIndex ? 'current' : 'pending')}}"
          data-index="{{index}}"
          bindtap="goToWord"
        ></view>
      </view>
      
      <button 
        class="nav-btn {{currentIndex === totalCount - 1 ? 'disabled' : ''}}" 
        bindtap="nextWord"
        disabled="{{currentIndex === totalCount - 1}}"
      >
        下一个 →
      </button>
    </view>
  </view>
</view>

<!-- 完成弹窗 -->
<view class="modal-overlay {{showCompleteModal ? 'show' : ''}}" bindtap="closeCompleteModal">
  <view class="complete-modal" catchtap="">
    <view class="modal-content">
      <view class="celebration-icon">🎉</view>
      <view class="modal-title">复习完成！</view>
      <view class="modal-subtitle">今天复习了 {{reviewResults.total}} 个字词</view>
      
      <view class="results-summary">
        <view class="result-item">
          <view class="result-number mastered">{{reviewResults.mastered}}</view>
          <view class="result-label">已掌握</view>
        </view>
        <view class="result-item">
          <view class="result-number need-review">{{reviewResults.needReview}}</view>
          <view class="result-label">需复习</view>
        </view>
      </view>
      
      <view class="modal-actions">
        <button class="btn-outline modal-btn" bindtap="closeCompleteModal">
          继续复习
        </button>
        <button class="btn-primary modal-btn" bindtap="goHome">
          返回首页
        </button>
      </view>
    </view>
  </view>
</view>
