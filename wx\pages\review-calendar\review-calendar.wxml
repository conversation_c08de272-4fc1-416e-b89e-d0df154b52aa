<!--复习日历页面-->
<view class="calendar-container">
  <!-- 月份导航 -->
  <view class="month-header">
    <view class="month-nav" bindtap="prevMonth">
      <text class="nav-icon">‹</text>
    </view>
    <view class="month-title">{{monthName}}</view>
    <view class="month-nav" bindtap="nextMonth">
      <text class="nav-icon">›</text>
    </view>
  </view>

  <!-- 月度统计 -->
  <view class="month-stats">
    <view class="stat-item">
      <view class="stat-number">{{monthStats.totalTasks}}</view>
      <view class="stat-label">总任务</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{monthStats.completedTasks}}</view>
      <view class="stat-label">已完成</view>
    </view>
    <view class="stat-item">
      <view class="stat-number">{{monthStats.completionRate}}%</view>
      <view class="stat-label">完成率</view>
    </view>
  </view>

  <!-- 星期标题 -->
  <view class="weekdays">
    <view class="weekday">一</view>
    <view class="weekday">二</view>
    <view class="weekday">三</view>
    <view class="weekday">四</view>
    <view class="weekday">五</view>
    <view class="weekday">六</view>
    <view class="weekday">日</view>
  </view>

  <!-- 日历网格 -->
  <view class="calendar-grid">
    <view 
      wx:for="{{calendarDays}}" 
      wx:key="index"
      class="calendar-day {{item.isCurrentMonth ? 'current-month' : 'other-month'}} {{item.isToday ? 'today' : ''}} {{item.hasTasks ? 'has-tasks' : ''}}"
      data-date-str="{{item.dateStr}}"
      data-has-tasks="{{item.hasTasks}}"
      bindtap="onDateTap"
    >
      <view class="day-number" wx:if="{{item.isCurrentMonth}}">{{item.date}}</view>
      
      <!-- 任务指示器 -->
      <view class="task-indicator" wx:if="{{item.hasTasks}}">
        <view class="task-dot {{item.completed === item.total ? 'completed' : 'pending'}}"></view>
        <text class="task-count">{{item.total}}</text>
      </view>
      
      <!-- 完成率指示 -->
      <view class="completion-bar" wx:if="{{item.hasTasks && item.total > 0}}">
        <view 
          class="completion-fill" 
          style="width: {{item.completionRate}}%"
        ></view>
      </view>
    </view>
  </view>

  <!-- 图例说明 -->
  <view class="legend">
    <view class="legend-item">
      <view class="legend-dot pending"></view>
      <text class="legend-text">有待复习任务</text>
    </view>
    <view class="legend-item">
      <view class="legend-dot completed"></view>
      <text class="legend-text">任务已完成</text>
    </view>
    <view class="legend-item">
      <view class="legend-dot today"></view>
      <text class="legend-text">今天</text>
    </view>
  </view>
</view>

<!-- 任务详情弹窗 -->
<view class="task-modal {{showTaskModal ? 'show' : ''}}" wx:if="{{showTaskModal}}">
  <view class="modal-mask" bindtap="closeTaskModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <view class="modal-title">{{selectedDate}} 复习任务</view>
      <view class="close-btn" bindtap="closeTaskModal">×</view>
    </view>
    
    <view class="modal-body">
      <view class="task-summary">
        共 {{selectedTasks.length}} 个字词需要复习
      </view>
      
      <view class="task-list">
        <view 
          wx:for="{{selectedTasks}}" 
          wx:key="plan_id"
          class="task-item"
        >
          <view class="task-word">{{item.word_info.word_text}}</view>
          <view class="task-info">
            <text class="task-stage">第{{item.review_stage}}次复习</text>
            <text class="task-status {{item.completed ? 'completed' : 'pending'}}">
              {{item.completed ? '已完成' : '待复习'}}
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="modal-footer">
      <button class="btn-secondary" bindtap="closeTaskModal">关闭</button>
      <button class="btn-primary" bindtap="startDateReview">开始复习</button>
    </view>
  </view>
</view>
