/* 首页样式 */

.home-container {
  background: var(--warm-beige);
  min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
  background: linear-gradient(135deg, var(--primary-green), var(--light-green));
  padding: 48rpx 32rpx;
  color: white;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-left {
  flex: 1;
}

.greeting {
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.welcome-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.welcome-right {
  text-align: right;
}

.greeting-icon {
  font-size: 48rpx;
  margin-bottom: 8rpx;
}

.child-info, .no-child {
  font-size: 24rpx;
  opacity: 0.8;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.no-child {
  color: rgba(255, 255, 255, 0.7);
}

/* 主要内容 */
.main-content {
  padding: 32rpx;
  margin-top: -16rpx;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.date-tag {
  background: var(--light-green);
  color: var(--text-dark);
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.word-count {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 进度区域 */
.progress-section {
  margin-bottom: 32rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-label {
  font-size: 28rpx;
  color: var(--text-light);
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: var(--primary-green);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 24rpx;
}

.action-btn {
  padding: 24rpx;
  font-size: 28rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

/* 字词列表 */
.word-list {
  margin-bottom: 24rpx;
}

.word-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  margin-bottom: 16rpx;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.word-item.completed {
  background: var(--soft-green);
}

.word-item.pending {
  background: #FFF3E0;
}

.word-display {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.word-char {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--text-dark);
  font-family: 'KaiTi', '楷体', serif;
}

.word-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.word-name {
  font-size: 32rpx;
  font-weight: 500;
  color: var(--text-dark);
}

.word-status {
  font-size: 24rpx;
  color: var(--text-light);
}

.word-item.completed .word-status {
  color: var(--primary-green);
}

.word-item.pending .word-status {
  color: var(--soft-orange);
}

.word-status-icon {
  font-size: 32rpx;
}

/* 查看全部按钮 */
.view-all-btn {
  text-align: center;
  color: var(--primary-green);
  font-size: 28rpx;
  font-weight: 500;
  padding: 16rpx;
  border-top: 1px solid var(--border-light);
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80rpx 32rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: var(--text-light);
  margin-bottom: 48rpx;
  line-height: 1.5;
}

/* 统计网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
  text-align: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: bold;
  color: var(--primary-green);
}

.stat-item:nth-child(2) .stat-number {
  color: var(--soft-orange);
}

.stat-item:nth-child(3) .stat-number {
  color: #3B82F6;
}

.stat-label {
  font-size: 24rpx;
  color: var(--text-light);
}

/* 响应式调整 */
@media (max-width: 375px) {
  .welcome-content {
    flex-direction: column;
    gap: 24rpx;
    text-align: center;
  }
  
  .welcome-right {
    text-align: center;
  }
  
  .action-buttons {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24rpx;
  }
}
